html{
    display: flex;
    flex-direction: column;
    min-height: 100%;
    overflow-x: hidden;
}

body{
    width: 100%;
    max-height: 570vh;
    min-height: 100%;
    max-width: 100%;
    position: relative;
    background-color: #ffd399;
    background-image: url(../../images/textures/leather.webp);
    background-size: cover;
    background-repeat: no-repeat;
    background-attachment: fixed;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    overflow-x: hidden; 
}

header{
    width: 100%;
    height: 8vh;
}

main{
    flex: 1;
}

footer{
    overflow-y: hidden;
}