#hdr{
    position: fixed;
    background: linear-gradient(#00060aab, #00060a00);
    z-index: 1;
}

#hdr > nav{
    height: inherit;
    width: inherit;
}

/* HEADER - Logo */

#app_name{
    display: flex;
    align-items: center;
    justify-content: center;
    padding-inline: 1.6rem;
    background-image: url(../images/medieval-elements/frames/rectangle-frame-3-darkbrown.webp);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 120%;
    width: fit-content;
    max-height: 5vh;
    min-height: min-content;
}

#notepad_icon{
    width: 1.5rem;
}

#app_name_text{
    font-family: "MedievalSharp", cursive;
    color: #f2a60d;
    text-shadow: 0rem -0.1rem rgba(255, 255, 255, 0.699);
    filter: contrast(1.2);
    padding-inline: 0.2rem;
    white-space: nowrap;
}

#anvil_icon{
    width: 1.8rem;
}

/* HEADER - Auth */

#auth{
    display: flex;
    align-items: center;
    padding-inline: 1.5rem;
}

#login_btn{
    position: relative;
    z-index: 1;
    font-family: "Pirata One", cursive;
    letter-spacing: 0.06rem;
    color: #f2a60dbf;
    text-shadow: 0rem -0.1rem #00000050;
    filter: contrast(1.2);
    padding: 0.1rem;
    padding-inline: 0.4rem;
    background-color: #57535f40;
    background: radial-gradient(circle, #4a535850, transparent);  
    border-radius: 0.8rem;
    white-space: nowrap;
}

/* FOOTER */

#ftr{
    display:none;
    background-color: #8c8796;
    height: 15vh;
}

#ftr > nav > ul > li{
    padding-inline: 2.5rem;
}

/* GERAL */

.no-bg-btn{
    cursor: pointer;
    background-color: transparent;
    border: none;
    display: flex;
}

.nav{
    display: flex;
    align-items: center;
}
